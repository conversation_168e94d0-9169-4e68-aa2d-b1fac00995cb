Code.require_file("support/test.ex", __DIR__)
Code.require_file("support/test_config.ex", __DIR__)
Code.require_file("support/test_relations.ex", __DIR__)
Code.require_file("support/doctest_case.ex", __DIR__)
Code.require_file("support/relation_case.ex", __DIR__)
Code.require_file("support/integration_case.ex", __DIR__)

# Doctest setup
Code.require_file("support/fixtures.ex", __DIR__)

# Clear cache before starting tests
Drops.Relation.Cache.clear_all()

# Start test repositories
Application.ensure_all_started(:ecto_sql)
Test.Repos.start(:all, :manual)
Test.Repos.start(MyApp.Repo, :manual)

Application.put_env(:drops_relation, :drops,
  relation: [
    ecto_schema_module: &Test.Config.ecto_schema_module/1
  ]
)

Application.put_env(:my_app, :drops,
  relation: [
    repo: MyApp.Repo,
    ecto_schema_module: &Test.Config.ecto_schema_module/1
  ]
)

Test.Repos.with_owner(MyApp.Repo, fn repo ->
  {:ok, _} = Drops.Relation.Cache.warm_up(repo, ["users", "posts"])
end)

defmodule MyApp.Users do
  use Drops.Relation, otp_app: :my_app

  schema("users", infer: true)
end

defmodule MyApp.Posts do
  use Drops.Relation, otp_app: :my_app

  schema("posts", infer: true)
end

ExUnit.start()
