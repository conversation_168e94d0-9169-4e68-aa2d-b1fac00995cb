defmodule Test.Relations do
  @moduledoc false
  def put(name, module) when is_atom(name) and is_atom(module) do
    test_id = Process.get(:test_id)

    if test_id do
      :persistent_term.put({:test_relations, test_id, name}, module)
    end

    module
  end

  def get(name) when is_atom(name) do
    test_id = Process.get(:test_id)

    if test_id do
      case :persistent_term.get({:test_relations, test_id, name}, nil) do
        nil ->
          relation_name = Macro.camelize(Atom.to_string(name))
          Module.concat([Test, Relations, "#{relation_name}#{test_id}"])

        relation_module ->
          relation_module
      end
    else
      relation_name = Macro.camelize(Atom.to_string(name))
      Module.concat([Test, Relations, relation_name])
    end
  end

  def cleanup(test_id) when is_integer(test_id) do
    :persistent_term.get()
    |> Enum.filter(fn
      {{:test_relations, ^test_id, _name}, _module} -> true
      _ -> false
    end)
    |> Enum.each(fn {key, _value} ->
      :persistent_term.erase(key)
    end)
  end
end
