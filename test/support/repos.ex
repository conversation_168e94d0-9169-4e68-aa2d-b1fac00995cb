defmodule Test.Repos do
  @moduledoc false

  @adapters [:sqlite, :postgres]

  def start(repo_or_adapter, mode \\ :manual)

  def start(:all, mode), do: Enum.each(@adapters, &start(&1, mode))
  def start(:sqlite, mode), do: start(Test.Repos.Sqlite, mode)
  def start(:postgres, mode), do: start(Test.Repos.Postgres, mode)

  def start(repo, mode) do
    {:ok, pid} = repo.start_link()

    :ok = Ecto.Adapters.SQL.Sandbox.mode(repo, mode)

    :persistent_term.put({:repos, repo}, pid)

    repo_pid(repo)
  end

  def stop_owner(:sqlite), do: stop_owner(Test.Repos.Sqlite)
  def stop_owner(:postgres), do: stop_owner(Test.Repos.Postgres)

  def stop_owner(repo) do
    case owner_pid(repo) do
      nil ->
        :ok

      :checked_out ->
        Ecto.Adapters.SQL.Sandbox.checkin(repo)
        Process.delete({:repos, repo, :owner})
        :ok

      pid when is_pid(pid) ->
        Ecto.Adapters.SQL.Sandbox.stop_owner(pid)
        Process.delete({:repos, repo, :owner})
        :ok
    end
  end

  def checkout!(repo, opts \\ [])

  def checkout!(:sqlite, opts), do: checkout!(Test.Repos.Sqlite, opts)
  def checkout!(:postgres, opts), do: checkout!(Test.Repos.Postgres, opts)

  def checkout!(repo, opts) when is_atom(repo) do
    case ensure_started(repo) do
      {:ok, _pid} ->
        case Ecto.Adapters.SQL.Sandbox.checkout(repo, opts) do
          :ok -> :ok
          {:already, _} -> :ok
        end

      {:error, error} ->
        raise "Failed to start repo #{repo}: #{inspect(error)}"
    end
  end

  def checkin!(repo, opts \\ [])

  def checkin!(:sqlite, opts), do: checkin!(Test.Repos.Sqlite, opts)
  def checkin!(:postgres, opts), do: checkin!(Test.Repos.Postgres, opts)

  def checkin!(repo, _opts) when is_atom(repo) do
    case Ecto.Adapters.SQL.Sandbox.checkin(repo) do
      :ok -> :ok
      # Already checked in or never checked out
      :not_found -> :ok
    end
  end

  def start_owner!(repo, opts \\ [])

  def start_owner!(:sqlite, opts), do: start_owner!(Test.Repos.Sqlite, opts)
  def start_owner!(:postgres, opts), do: start_owner!(Test.Repos.Postgres, opts)

  def start_owner!(repo, opts) do
    case ensure_started(repo) do
      {:ok, _pid} ->
        try do
          pid = Ecto.Adapters.SQL.Sandbox.start_owner!(repo, opts)
          Process.put({:repos, repo, :owner}, pid)
          :ok
        rescue
          error ->
            # Check if the error is due to already being in shared mode
            error_string = inspect(error)

            if String.contains?(error_string, ":already_shared") do
              # Repository is already in shared mode, just checkout a connection
              case Ecto.Adapters.SQL.Sandbox.checkout(repo, opts) do
                :ok ->
                  Process.put({:repos, repo, :owner}, :checked_out)
                  :ok

                {:already, _} ->
                  Process.put({:repos, repo, :owner}, :checked_out)
                  :ok
              end
            else
              if opts[:retry] <= 3 do
                retry = Keyword.put(opts, :retry, Keyword.get(opts, :retry, 0) + 1)
                start_owner!(repo, retry)
              else
                reraise error, __STACKTRACE__
              end
            end
        end

      {:error, error} ->
        raise "Failed to start repo #{repo}: #{inspect(error)}"
    end
  end

  def with_owner(repo, fun) do
    try do
      start_owner!(repo, shared: false)
      fun.(repo)
    rescue
      error ->
        reraise error, __STACKTRACE__
    after
      stop_owner(repo)
    end
  end

  def each_repo(fun) do
    Enum.each(Application.get_env(:drops_relation, :ecto_repos), &fun.(&1))
  end

  defp ensure_started(repo) do
    case Process.whereis(repo) do
      nil ->
        try do
          start(repo)

          {:ok, repo_pid(repo)}
        rescue
          error ->
            {:error, error}
        end

      pid ->
        {:ok, pid}
    end
  end

  defp repo_pid(repo) do
    :persistent_term.get({:repos, repo}, nil)
  end

  defp owner_pid(repo) do
    Process.get({:repos, repo, :owner}, nil)
  end
end

defmodule Test.Repos.Sqlite do
  @moduledoc false

  use Ecto.Repo,
    otp_app: :drops_relation,
    pool: Ecto.Adapters.SQL.Sandbox,
    adapter: Ecto.Adapters.SQLite3
end

defmodule Test.Repos.Postgres do
  @moduledoc false

  use Ecto.Repo,
    otp_app: :drops_relation,
    pool: Ecto.Adapters.SQL.Sandbox,
    adapter: Ecto.Adapters.Postgres
end

defmodule MyApp.Repo do
  @moduledoc false

  use Ecto.Repo,
    otp_app: :drops_relation,
    pool: Ecto.Adapters.SQL.Sandbox,
    adapter: Ecto.Adapters.Postgres
end
