defmodule Test.DoctestCase do
  use ExUnit.CaseTemplate

  using do
    quote do
      import unquote(__MODULE__)

      setup tags do
        if tags[:test_type] == :doctest do
          if tags[:async] do
            :ok = Ecto.Adapters.SQL.Sandbox.checkout(MyApp.Repo)
          else
            try do
              pid = Ecto.Adapters.SQL.Sandbox.start_owner!(MyApp.Repo, shared: true)
              Process.put({:doctest_owner, MyApp.Repo}, pid)
              :ok
            rescue
              error ->
                # Check if the error is due to already being in shared mode
                error_string = inspect(error)

                if String.contains?(error_string, ":already_shared") do
                  # Repository is already in shared mode, just checkout a connection
                  case Ecto.Adapters.SQL.Sandbox.checkout(MyApp.Repo) do
                    :ok ->
                      Process.put({:doctest_owner, MyApp.Repo}, :checked_out)
                      :ok

                    {:already, _} ->
                      Process.put({:doctest_owner, MyApp.Repo}, :checked_out)
                      :ok
                  end
                else
                  reraise error, __STACKTRACE__
                end
            end
          end

          {:ok, _} = Drops.Relation.Cache.warm_up(MyApp.Repo, ["users", "posts"])

          modules_before = Test.loaded_modules()

          fixtures = tags[:fixtures] || []

          Test.Fixtures.load(fixtures)

          on_exit(fn ->
            if tags[:async] do
              :ok = Ecto.Adapters.SQL.Sandbox.checkin(MyApp.Repo)
            else
              case Process.get({:doctest_owner, MyApp.Repo}) do
                nil ->
                  :ok

                :checked_out ->
                  :ok = Ecto.Adapters.SQL.Sandbox.checkin(MyApp.Repo)
                  Process.delete({:doctest_owner, MyApp.Repo})
                  :ok

                pid when is_pid(pid) ->
                  Ecto.Adapters.SQL.Sandbox.stop_owner(pid)
                  Process.delete({:doctest_owner, MyApp.Repo})
                  :ok
              end
            end

            new_modules = MapSet.difference(Test.loaded_modules(), modules_before)
            test_module_prefix = to_string(__MODULE__)

            Enum.each(new_modules, fn module ->
              module_string = to_string(module)

              if String.starts_with?(module_string, test_module_prefix) do
                Test.clear_module(module_string)
              end

              if function_exported?(module, :schema, 0) do
                Test.cleanup_relation_modules(module)
              end
            end)
          end)
        end

        :ok
      end
    end
  end
end
