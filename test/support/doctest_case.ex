defmodule Test.DoctestCase do
  use ExUnit.CaseTemplate

  using do
    quote do
      import unquote(__MODULE__)

      setup tags do
        if tags[:test_type] == :doctest do
          if tags[:async] do
            :ok = Ecto.Adapters.SQL.Sandbox.checkout(MyApp.Repo)
          else
            pid = Ecto.Adapters.SQL.Sandbox.start_owner!(MyApp.Repo, shared: true)
            Process.put({:doctest_owner, MyApp.Repo}, pid)
            :ok
          end

          {:ok, _} = Drops.Relation.Cache.warm_up(MyApp.Repo, ["users", "posts"])

          modules_before = Test.loaded_modules()

          fixtures = tags[:fixtures] || []

          Test.Fixtures.load(fixtures)

          on_exit(fn ->
            if tags[:async] do
              :ok = Ecto.Adapters.SQL.Sandbox.checkin(MyApp.Repo)
            else
              case Process.get({:doctest_owner, MyApp.Repo}) do
                nil ->
                  :ok

                pid when is_pid(pid) ->
                  Ecto.Adapters.SQL.Sandbox.stop_owner(pid)
                  Process.delete({:doctest_owner, MyApp.Repo})
                  :ok
              end
            end

            new_modules = MapSet.difference(Test.loaded_modules(), modules_before)
            test_module_prefix = to_string(__MODULE__)

            Enum.each(new_modules, fn module ->
              module_string = to_string(module)

              if String.starts_with?(module_string, test_module_prefix) do
                Test.clear_module(module_string)
              end

              if function_exported?(module, :schema, 0) do
                Test.cleanup_relation_modules(module)
              end
            end)
          end)
        end

        :ok
      end
    end
  end
end
