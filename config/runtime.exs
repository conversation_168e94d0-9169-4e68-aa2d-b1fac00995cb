import Config

env = config_env()

# Support for test partitioning - append partition number to database names
partition_suffix =
  case System.get_env("MIX_TEST_PARTITION") do
    nil -> ""
    partition -> "_partition_#{partition}"
  end

config :logger, :default_handler, config: [file: ~c"log/#{env}.log"]

config :drops_relation, Test.Repos.Sqlite,
  adapter: Ecto.Adapters.SQLite3,
  database: "priv/repo/#{env}#{partition_suffix}.sqlite",
  pool: Ecto.Adapters.SQL.Sandbox,
  priv: "priv/repo/sqlite",
  log: :debug

config :drops_relation, Test.Repos.Postgres,
  adapter: Ecto.Adapters.Postgres,
  username: "postgres",
  password: "postgres",
  hostname: System.get_env("POSTGRES_HOST", "postgres"),
  database: "drops_relation_#{env}#{partition_suffix}",
  pool: Ecto.Adapters.SQL.Sandbox,
  priv: "priv/repo/postgres",
  log: :debug

config :drops_relation, MyApp.Repo,
  adapter: Ecto.Adapters.Postgres,
  username: "postgres",
  password: "postgres",
  hostname: System.get_env("POSTGRES_HOST", "postgres"),
  database: "drops_relation_#{env}_my_app#{partition_suffix}",
  pool: Ecto.Adapters.SQL.Sandbox,
  priv: "priv/repo/postgres",
  log: :debug
